datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

enum Role {
  ADMIN
  USER
}

model User {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  email      String  @unique
  password   String
  role       Role    @default(USER)
  isVerified Boolean @default(false)
  isBlocked  Boolean @default(false)

  otp                Otp?
  profile            Profile?
  notes              UserNote[]
  securityViolations SecurityViolation[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum OtpType {
  VERIFY_EMAIL
  RESET_PASSWORD
}

enum ViolationType {
  COPY_PASTE
  SCREENSHOT
  CONTEXT_MENU
  DEVELOPER_TOOLS
}

model SecurityViolation {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  type        ViolationType
  description String
  userAgent   String?
  ipAddress   String?

  userId String @db.ObjectId
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Otp {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  code String
  type OtpType @default(VERIFY_EMAIL)

  userId String @unique @map("user_id") @db.ObjectId
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
}

model Profile {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  firstName String
  lastName  String
  isStudent Boolean @default(false)
  notify    Boolean @default(true)

  userId String @unique @db.ObjectId
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Block {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  blockOrder            Int
  blockTitle            String
  blockDescription      String
  guideLink             String
  guideDescription      String
  weeksDescription      String
  flashcardsDescription String
  sampleTestDescription String
  finalTestDescription  String
  isPublished           Boolean @default(false)
  isFlashcardsEnabled   Boolean @default(false)
  isSampleTestEnabled   Boolean @default(false)
  isFinalTestEnabled    Boolean @default(false)

  weeks               Week[]
  flashcards          Flashcard[]
  sampleTestQuestions SampleTestQuestion[]
  finalTestQuestions  FinalTestQuestion[]
  blockUserNotes      UserNote[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Week {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  weekNumber Int
  title      String

  blockId String @db.ObjectId
  block   Block  @relation(fields: [blockId], references: [id], onDelete: Cascade)

  presentations Presentation[]
  audios        Audio[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Presentation {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  title            String
  presentationLink String

  weekId String @db.ObjectId
  week   Week   @relation(fields: [weekId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Audio {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  title     String
  audioLink String

  weekId String @db.ObjectId
  week   Week   @relation(fields: [weekId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Flashcard {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  question String
  answer   String

  blockId String @db.ObjectId
  block   Block  @relation(fields: [blockId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model SampleTestQuestion {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  question      String
  answers       String[]
  correctAnswer String

  blockId String @db.ObjectId
  block   Block  @relation(fields: [blockId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model FinalTestQuestion {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  question      String
  answers       String[]
  correctAnswer String

  blockId String @db.ObjectId
  block   Block  @relation(fields: [blockId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model UserNote {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  content String

  blockId String @db.ObjectId
  block   Block  @relation(fields: [blockId], references: [id], onDelete: Cascade)
  userId  String @db.ObjectId
  user    User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
