{"name": "@zedsols/psychtechlearning", "version": "0.1.0", "private": true, "scripts": {"biome:check": "biome check", "biome:safe": "biome check --write", "biome:unsafe": "biome check --write --unsafe", "prisma": "prisma", "ui": "shadcn", "postinstall": "prisma generate", "dev": "next dev --turbopack", "build": "next build", "start": "next start"}, "packageManager": "pnpm@10.11.1", "dependencies": {"@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.9.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.80.5", "@vercel/analytics": "^1.5.0", "argon2": "^0.41.1", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.16.0", "html2canvas": "^1.4.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.475.0", "next": "15.1.7", "next-themes": "^0.4.6", "nodemailer": "^7.0.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "react-hot-toast": "^2.5.2", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.51"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.29", "@types/nodemailer": "^6.4.17", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "postcss": "^8.5.4", "prisma": "^6.9.0", "shadcn": "^2.6.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}